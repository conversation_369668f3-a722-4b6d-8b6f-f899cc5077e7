<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::get('test', [App\Http\Controllers\Website\TestController::class, 'test'])->name('test');


Route::group(['middleware' => ['localization']], function () {

    Route::group(['prefix' => 'auth'], function () {
        Route::post('login', [\App\Http\Controllers\Auth\AuthController::class, 'login']);
        Route::post('register', [\App\Http\Controllers\Auth\AuthController::class, 'register']);
        Route::post('/forgot-password-phone', [\App\Http\Controllers\Auth\AuthController::class, 'forgotPasswordPhone']);
        Route::post('/forgot-password-email', [\App\Http\Controllers\Auth\AuthController::class, 'forgotPasswordEmail']);
        Route::post('/verification-code', [\App\Http\Controllers\Auth\AuthController::class, 'verifyCode']);

        Route::post('/send-otp-code', [\App\Http\Controllers\Auth\AuthController::class, 'sendOTPVerifyCode']);
        Route::post('/verification-otp-code', [\App\Http\Controllers\Auth\AuthController::class, 'verifyOTPCode']);

        Route::get('/verification-token/{token}', [\App\Http\Controllers\Auth\AuthController::class, 'verifyToken']);
        Route::post('/reset-password-code', [\App\Http\Controllers\Auth\AuthController::class, 'resetPasswordCode']);
        Route::post('/reset-password-token/{token}', [\App\Http\Controllers\Auth\AuthController::class, 'resetPasswordToken']);



        Route::middleware('auth:api')->delete('logout', [\App\Http\Controllers\Auth\AuthController::class, 'logout']);

        // Route::post('refresh', [\App\Http\Controllers\Auth\LoginController::class, 'refresh']);
        // Route::get('me', [\App\Http\Controllers\Auth\AuthController::class, 'me']);
    });


    // Route::middleware("throttle:300,60")->group(function () {
    // Rate-limited routes
    //Route::get('autocomplete', [App\Http\Controllers\Website\SearchController::class, 'autocomplete'])->name('autocomplete');

    Route::get('autocomplete', [App\Http\Controllers\Website\SearchProductController::class, 'autocomplete'])->name('autocomplete');
    Route::get('search', [App\Http\Controllers\Website\SearchProductController::class, 'search']);
    Route::get('products', [App\Http\Controllers\Website\SearchProductController::class, 'products']);
    Route::get('most-popular', [\App\Http\Controllers\Website\SearchProductController::class, 'mostPopular']);
    Route::get('new-arrival', [\App\Http\Controllers\Website\SearchProductController::class, 'newArrival']);
    Route::get('offers', [\App\Http\Controllers\Website\SearchProductController::class, 'offers']);
    Route::get('home-offers', [\App\Http\Controllers\Website\SearchProductController::class, 'homeOffers']);
    Route::get('products/history', [\App\Http\Controllers\Website\SearchProductController::class, 'productsHistory']);


    // });
    // Route::get('search', [App\Http\Controllers\Website\SearchController::class, 'index']);

    Route::get('categories/{category}', [\App\Http\Controllers\Website\CategoryController::class, 'show']);

    Route::get('categories', [\App\Http\Controllers\Website\CategoryController::class, 'index']);

    Route::get('used-categories/{usedCategory}', [\App\Http\Controllers\Website\UsedCategoryController::class, 'show']);
    Route::get('used-categories', [\App\Http\Controllers\Website\UsedCategoryController::class, 'index']);

    // Route::get('products', [\App\Http\Controllers\Website\SearchController::class, 'index']);
    Route::get('variances', [\App\Http\Controllers\Website\VarianceSearchController::class, 'index']);

    Route::get('products/brand/{slug}', [\App\Http\Controllers\Website\SearchController::class, 'searchByBrand']);

    Route::get('products/offers', [\App\Http\Controllers\Website\SearchController::class, 'searchOffers']);

    Route::middleware('auth:api')->post('products/{product}/rating', [\App\Http\Controllers\Website\ProductController::class, 'storeRatingOnProduct']);
    Route::middleware('auth:api')->put('products/{product}/rating/{ratingId}', [\App\Http\Controllers\Website\ProductController::class, 'updateRatingOnProduct']);

    Route::middleware('auth:api')->post('ratings', [\App\Http\Controllers\Website\RatingController::class, 'store']);
    Route::middleware('auth:api')->put('ratings/{model}', [\App\Http\Controllers\Website\RatingController::class, 'update']);


    Route::middleware('authOrVisitor')->get('complaints', [\App\Http\Controllers\Website\ComplaintController::class, 'index']);
    Route::middleware('authOrVisitor')->post('complaints/rating', [\App\Http\Controllers\Website\ComplaintController::class, 'store']);
    Route::middleware('authOrVisitor')->put('complaints/rating/{ratingComplaint}', [\App\Http\Controllers\Website\ComplaintController::class, 'update']);

    Route::middleware('authOrVisitor')->apiResource('fcm-token', \App\Http\Controllers\Website\FCMTokenController::class);//->only(['store', 'update']);


    Route::middleware('authOrVisitor')->post('subscribe/stock', [\App\Http\Controllers\Website\SubscribeController::class, 'subscribe']);



    Route::get('products/{product}/{variance?}', [\App\Http\Controllers\Website\ProductController::class, 'getProductDetails']);

    Route::get('products-by-old-slug/{slug}', [\App\Http\Controllers\Website\ProductController::class, 'getProductDetailsByOldSlug']);
    Route::get('products-by-old-id/{oldId}', [\App\Http\Controllers\Website\ProductController::class, 'getProductDetailsByOldId']);

    Route::post('products/{product}/{variance?}', [\App\Http\Controllers\Website\ProductController::class, 'getProductVarianceDetails']);

    // Route::get('most-popular', [\App\Http\Controllers\Website\SearchController::class, 'mostPopular']);
    // Route::get('new-arrival', [\App\Http\Controllers\Website\SearchController::class, 'newArrival']);

    Route::get('suggested-product/{productId}', [\App\Http\Controllers\Website\SearchProductController::class, 'suggestedProducts']);



    Route::get('used-products', [\App\Http\Controllers\Website\UsedProductController::class, 'index']);
    Route::get('used-products/{usedProduct}', [\App\Http\kControllers\Website\UsedProductController::class, 'show']);

    Route::middleware('auth:api')->get('my/used-products', [\App\Http\Controllers\Website\UsedProductController::class, 'getUserUsedProducts']);
    Route::middleware('auth:api')->post('my/used-products', [\App\Http\Controllers\Website\UsedProductController::class, 'addUsedProductByUser']);
    Route::middleware('auth:api')->get('my/used-products/{usedProduct}', [\App\Http\Controllers\Website\UsedProductController::class, 'showUserUsedProduct']);
    Route::middleware('auth:api')->put('my/used-products/{usedProduct}', [\App\Http\Controllers\Website\UsedProductController::class, 'updateUserUsedProduct']);
    Route::middleware('auth:api')->get('my/used-products/details/{usedProduct}', [\App\Http\Controllers\Website\UsedProductController::class, 'showUserUsedProductDetails']);
    Route::middleware('auth:api')->delete('my/used-products/{usedProduct}', [\App\Http\Controllers\Website\UsedProductController::class, 'destroy']);


    Route::middleware('auth:api')->get('my/ratings', [\App\Http\Controllers\Website\RatingController::class, 'index']);

    Route::middleware('auth:api')->post('products/{product}/rating', [\App\Http\Controllers\Website\ProductController::class, 'storeRatingOnProduct']);
    Route::get('ratings/{product}', [\App\Http\Controllers\Website\ProductController::class, 'getRatingsProduct']);

    Route::middleware('auth:api')->get('my/wishlist', [\App\Http\Controllers\Website\WishlistController::class, 'getUserWishlist']);
    Route::middleware('auth:api')->post('my/wishlist', [\App\Http\Controllers\Website\WishlistController::class, 'addToWishlist']);
    Route::middleware('auth:api')->delete('my/wishlist/{product}', [\App\Http\Controllers\Website\WishlistController::class, 'removeFromWishlist']);

    Route::middleware('authOrVisitor')->get('wishlist', [\App\Http\Controllers\Website\WishlistController::class, 'getUserWishlist']);
    Route::middleware('authOrVisitor')->post('wishlist', [\App\Http\Controllers\Website\WishlistController::class, 'addToWishlist']);
    Route::middleware('authOrVisitor')->delete('wishlist/{product}', [\App\Http\Controllers\Website\WishlistController::class, 'removeFromWishlist']);



    Route::middleware('auth:api')->apiResource('/my/addresses', \App\Http\Controllers\Website\AddressController::class);
    Route::middleware('auth:api')->put('/my/addresses/set-default-address/{address}', [\App\Http\Controllers\Website\AddressController::class, 'setDefaultAddress']);



    Route::middleware('auth:api')->get('/my/orders', [\App\Http\Controllers\Website\OrderController::class, 'index']);
    Route::middleware('auth:api')->get('/my/orders/{order}', [\App\Http\Controllers\Website\OrderController::class, 'show']);
    Route::middleware('auth:api')->delete('/my/orders/{order}', [\App\Http\Controllers\Website\OrderController::class, 'destroy']);


    Route::middleware('authOrVisitor')->post('/my/orders/{order}/cancel', [\App\Http\Controllers\Website\OrderController::class, 'cancel']);

    Route::middleware('authOrVisitor')->get('/orders/{order}', [\App\Http\Controllers\Website\OrderController::class, 'orderDetails']);
    Route::middleware('authOrVisitor')->post('orders/store', [\App\Http\Controllers\Website\OrderController::class, 'store']);
    Route::middleware('authOrVisitor')->post('orders/buy-now', [\App\Http\Controllers\Website\OrderController::class, 'buyNow']);
    Route::middleware('authOrVisitor')->post('orders/store', [\App\Http\Controllers\Website\OrderController::class, 'store']);
    Route::middleware('authOrVisitor')->post('orders/{order}/shipping', [\App\Http\Controllers\Website\OrderController::class, 'setShippingCarrier']);
    Route::middleware('authOrVisitor')->post('orders/{order}/shipping-carriers', [\App\Http\Controllers\Website\OrderController::class, 'setShippingCarrier']);
    Route::middleware('authOrVisitor')->post('orders/{order}/promotion', [\App\Http\Controllers\Website\OrderController::class, 'setPromotion']);
    Route::middleware('authOrVisitor')->post('orders/{order}/set-payment', [\App\Http\Controllers\Website\OrderController::class, 'setPaymentMethod']);
    Route::middleware('authOrVisitor')->post('orders/{order}/complete', [\App\Http\Controllers\Website\OrderController::class, 'orderComplete']);
    Route::middleware('authOrVisitor')->post('orders/{order}/render-pay', [\App\Http\Controllers\Website\OrderController::class, 'renderPay']);
    Route::middleware('authOrVisitor')->post('orders/{order}/add-note', [\App\Http\Controllers\Website\OrderController::class, 'addNote']);
    Route::middleware('authOrVisitor')->post('orders/{order}/set-payment-params', [\App\Http\Controllers\Website\OrderController::class, 'setPaymentParams']);

    Route::middleware('auth:api')->post('orders/{order}/rating', [\App\Http\Controllers\Website\OrderController::class, 'storeRatingOnOrder']);

    Route::middleware('auth:api')->put('orders/{order}/rating/{rating}', [\App\Http\Controllers\Website\OrderController::class, 'updateRatingOnOrder']);


    // :TODO remove this function
    // Route::middleware('authOrVisitor')->post('orders/{order}/verify', [\App\Http\Controllers\Website\OrderController::class, 'verify']);


    Route::middleware('authOrVisitor')->get('orders/{order}/verify', [\App\Http\Controllers\Website\OrderController::class, 'verifyIsPaid']);
    Route::middleware('authOrVisitor')->post('orders/{order}/finalize', [\App\Http\Controllers\Website\OrderController::class, 'finalize']);
    Route::middleware('authOrVisitor')->get('orders/{order}/shippings', [\App\Http\Controllers\Website\OrderController::class, 'getShippings']);
    Route::middleware('authOrVisitor')->get('orders/{order}/shipping-carriers', [\App\Http\Controllers\Website\OrderController::class, 'getShippingCarriers']);
    Route::middleware('authOrVisitor')->get('orders/{order}/payment-methods', [\App\Http\Controllers\Website\OrderController::class, 'getAllowPaymentMethods']);
    Route::middleware('authOrVisitor')->post('orders/{order}/cancel', [\App\Http\Controllers\Website\OrderController::class, 'cancel']);
    Route::middleware('authOrVisitor')->post('orders/{order}/canceled', [\App\Http\Controllers\Website\OrderController::class, 'canceled']);


    // :TODO remove the function
    Route::middleware('authOrVisitor')->post('orders/{order}/verify-order-payment-hyperpay', [\App\Http\Controllers\Website\OrderController::class, 'verifyOrderPaymentHyperpay']);

    Route::middleware('authOrVisitor')->post('orders/{order}/address', [\App\Http\Controllers\Website\OrderController::class, 'address']);
    Route::middleware('authOrVisitor')->apiResource('/addresses', \App\Http\Controllers\Website\AddressController::class);
    Route::middleware('auth:api')->get('/my/profile', [\App\Http\Controllers\Website\AccountController::class, 'getUserGeneralInformation']);
    Route::middleware('auth:api')->put('/my/update-Information', [\App\Http\Controllers\Website\AccountController::class, 'updateUserInformation']);
    Route::middleware('auth:api')->put('/my/update-phone-number', [\App\Http\Controllers\Website\AccountController::class, 'updateUserPhoneNumber']);
    Route::middleware('auth:api')->put('/my/update-email', [\App\Http\Controllers\Website\AccountController::class, 'updateUserEmail']);
    Route::middleware('auth:api')->post('/my/update-image', [\App\Http\Controllers\Website\AccountController::class, 'updateUserImage']);
    Route::middleware('auth:api')->put('/my/update-password', [\App\Http\Controllers\Website\AccountController::class, 'updatePassword']);
    Route::middleware('auth:api')->post('/my/request-change', [\App\Http\Controllers\Website\AccountController::class, 'requestChange']);
    Route::middleware('auth:api')->get('/my/transactions', [\App\Http\Controllers\Website\AccountController::class, 'getTransactions']);
    Route::middleware('auth:api')->post('/my/verify-otp', [\App\Http\Controllers\Website\AccountController::class, 'verifyUserOtp']);

    Route::middleware('auth:api')->apiResource('my/fcm-token', \App\Http\Controllers\Website\FCMTokenController::class);

    Route::get('brands', [\App\Http\Controllers\Website\BrandController::class, 'index']);
    Route::get('brands/{brand}', [\App\Http\Controllers\Website\BrandController::class, 'show']);

    Route::middleware('authOrVisitor')->post('cart', [\App\Http\Controllers\Website\CartController::class, 'storeProductToCart']);
    Route::middleware('authOrVisitor')->put('cart', [\App\Http\Controllers\Website\CartController::class, 'updateCart']);
    Route::middleware('authOrVisitor')->get('cart', [\App\Http\Controllers\Website\CartController::class, 'getCart']);
    Route::middleware('authOrVisitor')->delete('cart/{cartId}', [\App\Http\Controllers\Website\CartController::class, 'deleteCart']);


    Route::get('pages', [\App\Http\Controllers\Website\PageController::class, 'index']);
    Route::get('pages/{page}', [\App\Http\Controllers\Website\PageController::class, 'show']);

    Route::get('branches', [\App\Http\Controllers\Website\BranchController::class, 'index']);
    Route::post('contact-us', [\App\Http\Controllers\Website\ContactUsController::class, 'store']);
    Route::get('banners', [\App\Http\Controllers\Website\BannerController::class, 'index']);

    Route::get('lookups-website/categories', [\App\Http\Controllers\Lookups\CategoryLookupController::class, 'index']);
    Route::get('lookups-website/brands', [\App\Http\Controllers\Lookups\BrandLookupController::class, 'index']);
    Route::get('lookups-website/{resource}', \App\Http\Controllers\Lookups\DynamicLookupController::class);

    Route::middleware('auth:api')->post('promotions/checked', [\App\Http\Controllers\Website\PromotionController::class, 'checked']);

    Route::middleware('auth:api')->get('notifications', [\App\Http\Controllers\Website\NotificationController::class, 'index']);
    Route::middleware('auth:api')->put('notifications/{notificationId}/read-it', [\App\Http\Controllers\Website\NotificationController::class, 'readIt']);
    Route::middleware('auth:api')->apiResource('wallet/payment-methods', \App\Http\Controllers\Website\WalletController::class)->only(['index']);
    Route::middleware('auth:api')->post('wallet/payment-methods', [\App\Http\Controllers\Website\WalletController::class, 'set']);
});

Route::group(['middleware' => ['localization']], function () {
    Route::get('lookups/translations/{abbreviation}', \App\Http\Controllers\Lookups\TranslationLookupController::class);
    //Route::post('media/upload', [\App\Http\Controllers\UploadMediaController::class, 'upload']);
    Route::post('media/upload', [\App\Http\Controllers\General\UploadMediaController::class, 'upload']);
    Route::post('upload-media', [\App\Http\Controllers\General\UploadMediaController::class, 'upload']); //! don't remove

    Route::delete('media/{id}', [\App\Http\Controllers\General\UploadMediaController::class, 'deleteMediaById']);

    // Route::get('lookups/languages', LanguageLookupController::class);

    Route::get('lookups/categories', [\App\Http\Controllers\Lookups\CategoryLookupController::class, 'index']);
    Route::get('lookups/{resource}', \App\Http\Controllers\Lookups\DynamicLookupController::class);
    Route::get('compare', [\App\Http\Controllers\Website\CompareController::class, 'index']);
});

//Route::get('shippings', [\App\Http\Controllers\Website\ShippingController::class, 'index']);

// Route::get('test', [\App\Http\Controllers\TestControllerApi::class, 'index']);

Route::get('routes', [\App\Http\Controllers\General\RouteController::class, 'index']);
Route::get('routes/admin', [\App\Http\Controllers\General\RouteController::class, 'routesAdmin']);

Route::get('sliders', [\App\Http\Controllers\Website\SliderController::class, 'index']);

Route::post('payment-webhook', \App\Http\Controllers\General\PaymentController::class);