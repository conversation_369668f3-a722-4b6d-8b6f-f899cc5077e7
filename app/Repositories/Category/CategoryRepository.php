<?php

namespace App\Repositories\Category;

use App\Exceptions\ModelNotFoundException;
use App\Models\Category;
use App\Repositories\BaseRepository;
use App\Repositories\Brand\BrandRepository;
use Illuminate\Database\Eloquent\Model;
use App\Helper\SlugHelper;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Spatie\Activitylog\Facades\LogBatch;


class CategoryRepository extends BaseRepository implements CategoryRepositoryInterface
{
    public $modelClass = Category::class;


    private function getOne($slug)
    {

        $query = $this->query()->with([
            'brands',
            "children",
            "children.media",
            "parent",
            "parent.parent",
            "parent.parent.parent",
            "media",
            'brands.media',
        ]);

        $queryJsonSlug = clone $query;

        $categoryModel = $query->where('slug', '=', $slug)->first();

        if (!$categoryModel) {
            $categoryModel = $queryJsonSlug->whereJsonContains('oldSlug->' . current_locale(), $slug)->first();
            if ($categoryModel) {
                // If found by oldSlug, construct the redirect URL
                $redirectUrl = $categoryModel->slug;
                $parent = $categoryModel->parent;
                while ($parent) {
                    $redirectUrl = $parent->slug . '/' . $redirectUrl;
                    $parent = $parent?->parent;
                }
                throw new \App\Exceptions\ModelNotFoundException('Category found by oldSlug, redirecting...', 301, $redirectUrl);
            } else {
                // If not found by either slug or oldSlug, throw the original exception
                throw new \Illuminate\Database\Eloquent\ModelNotFoundException('Category not found');
            }
        }

        return $categoryModel;

    }


    public function storeCategory(array $data): Model
    {

        $category = null;
        LogBatch::startBatch('batch_uuid');
        DB::transaction(function () use ($data, &$category) {
            $parentId = isset($data['parentId']) ? $data['parentId'] : null;
            $slug = $this->modelClass::generateSlug($data['name']['en'], $parentId);
            $filtersGroupsId = isset($data['filtersGroupsId']) ? $data['filtersGroupsId'] : null;

            $category = $this->store(
                [
                    'name' => $data['name'],
                    'slug' => $slug,
                    'sort' => $data['sort'] ?? 0,
                    'parentId' => $parentId,
                    'filtersGroupsId' => $filtersGroupsId,
                    'isShowBrandIngListing' => $data['isShowBrandIngListing'] ?? false,
                    'metaTitle' => $data['metaTitle'] ?? null,
                    'metaDescription' => $data['metaDescription'] ?? null,
                    'priority' => $data['priority'] ?? 0
                ]
            );

            if (isset($data['media']['cover']['src'])) {
                $category->addMediaToModel($data['media']['cover'], 'cover');
            }


            $category->categoriesAttributes()->sync(collect($data['categoriesAttributes'] ?? [])->map(function ($item) {
                return ['attributeId' => $item];
            }));


            $category->categoriesBrands()->sync(collect($data['categoriesBrands'] ?? [])->map(function ($item) {
                return ['brandId' => $item];
            }));


        });

        LogBatch::endBatch();

        return $category;
    }

    public function updateCategory(int|string $id, array $data): Model
    {

        $category = null;

        LogBatch::startBatch('batch_uuid');
        DB::transaction(function () use ($id, $data, &$category) {
            $parentId = isset($data['parentId']) ? $data['parentId'] : null;
            $filtersGroupsId = isset($data['filtersGroupsId']) ? $data['filtersGroupsId'] : null;
            $category = $this->update(
                $id,
                [
                    'name' => $data['name'],
                    'parentId' => $parentId,
                    'sort' => $data['sort'] ?? 0,
                    'filtersGroupsId' => $filtersGroupsId,
                    'metaTitle' => $data['metaTitle'],
                    'metaDescription' => $data['metaDescription'],
                    'priority' => $data['priority'] ?? 0,
                    'isShowBrandIngListing' => $data['isShowBrandIngListing'] ?? false,
                ]
            );

            $category->categoriesAttributes()->sync(collect($data['categoriesAttributes'] ?? [])->map(function ($item) {
                return ['attributeId' => $item];
            }));


            $category->categoriesBrands()->sync(collect($data['categoriesBrands'] ?? [])->map(function ($item) {
                return ['brandId' => $item];
            }));

        });

        LogBatch::endBatch();

        if (isset($data['media']['cover']['src'])) {
            $category->addMediaToModel($data['media']['cover'], 'cover');
        }


        return $category;
    }



    public function getAllCategoriesFiltered()
    {

        return Cache::remember('categories', $this->minutes, function () {
            return $this->query()->with([
                'media',
                'parent',
                'parent.media',
                'children',
                'children.media',
                'children.children',
                'children.children.media',
                'children.children.children',
                'children.children.children.media',
            ])
                ->whereNull('parentId')
                ->get();
        });

    }



    public function getAllCategoriesCached($stringQuery)
    {
        return Cache::remember("categories_$stringQuery", $this->minutes, function () {
            return $this->query()->filters()->with([
                'cover',
                'parent',
                'parent.cover',
                'children',
                'children.cover',
                'children.children',
                'children.children.cover',
                'children.children.children',
                'children.children.children.cover',
            ])
                ->whereNull('parentId')
                ->get();
        });

    }



    public function getAllCategories()
    {
        return $this->getAllLatestPaginatedFiltered(with: [
            'media',
            'parent',
            'children',
            'media'
        ]);


    }
    public function findByIdCategory(int|string $id): mixed
    {
        return $this->findById(
            $id,
            with:
            [
                'brands:brandId,name',
                'parent',
                'children',
                'attributes:attributeId,name',
                'media',
            ]
        );

    }


    public function findBySlugCategoryLevels(int|string $category, array $slugs): mixed
    {

        $with = [
            'brands',
            "children",
            "children.media",
            "parent",
            "parent.parent",
            "parent.parent.parent",
            "media",
            'brands.media',
        ];

        if (count($slugs) > 1) {
            $categories = $this->checkCategoriesParents($slugs);
            $category = $categories->where('slug', '=', $category)->first();
            $categoryModel = $this->findById($category->categoryId, ['*'], $with);
        } else {

            $categoryModel = $this->query()->with($with)->where('slug', '=', $category)->firstOrFail();

        }

        return $categoryModel;

    }

    public function findBySlugCategory(int|string $category, int|string $subcategory = null): mixed
    {
        $query = $this->query()->with([
            'brands',
            "children",
            "parent"
        ]);


        if (is_null($subcategory)) {
            return $query->where('slug', '=', trim($category))->firstorfail();
        }
        return $query->where('slug', '=', trim($subcategory))->firstorfail();



    }

    public function deleteCategory(int|string $id): mixed
    {
        return $this->destroy($id);
    }


    public function getCategoryAttributes(Category $category): array
    {
        return $category->categoriesAttributes()->with('options')->get()->toArray();
    }

    public function getCategoryBySlug(string $slug): mixed
    {

        $categoryModel = $this->query()->where('slug', '=', $slug)->first();

        if (!$categoryModel) {
            // Try to categoryModel the product by checking the 'oldSlug' JSON field
            $categoryModel = $this->query()->whereJsonContains('oldSlug->' . current_locale(), $slug)->first();

            if ($categoryModel) {
                // If found by oldSlug, construct the redirect URL
                $redirectUrl = $categoryModel->slug;
                $parent = $categoryModel->parent;
                while ($parent) {
                    $redirectUrl = $parent->slug . '/' . $redirectUrl;
                    $parent = $parent?->parent;
                }
                throw new \App\Exceptions\ModelNotFoundException('Category found by oldSlug, redirecting...', 301, $redirectUrl);
            } else {
                // If not found by either slug or oldSlug, throw the original exception
                throw new \Illuminate\Database\Eloquent\ModelNotFoundException('Product not found');
            }
        }
        return $categoryModel;

        // if (containsArabic($slug)) {
        //     return $this->query()->whereJsonContains('oldSlug->' . current_locale(), trim($slug))->firstorfail();
        // } else {
        //     return $this->query()->where('slug', '=', $slug)->firstorfail();
        // }



    }



    public function getCategoriesBySlugs($categories)
    {
        return $this->query()->whereIn('slug', $categories)->get();
    }





    public function buildCategoryHierarchy($categories, $parentSlug = null)
    {
        $hierarchy = [];


        foreach ($categories as $category) {
            if ($parentSlug === null || $category?->parent?->slug === $parentSlug) {
                $hierarchy[] = [
                    'slug' => $category->slug,
                    'name' => $category->name,
                    'children' => $this->buildCategoryHierarchy($categories, $category->slug),
                ];
            }
        }
        return $hierarchy;
    }

    public function checkCategoriesParents($slugs)
    {
        $categories = collect([]);
        $parentId = null;
        $validHierarchy = true;

        foreach ($slugs as $index => $slug) {
            // Query the category with the current slug and correct parent
            $category = $this->query()
                ->where('slug', $slug)
                ->when($index > 0, function ($query) use ($parentId): mixed {
                    return $query->where('parentId', $parentId);
                })
                ->first();
            if (is_null($category)) {

                $validHierarchy = false;
                break;
            }

            $categories->push($category);
            $parentId = $category->categoryId;
        }


        if (!$validHierarchy || count($categories) !== count($slugs)) {
            // Handle invalid hierarchy (your existing oldSlug check or throw exception)
            throw new \Illuminate\Database\Eloquent\ModelNotFoundException('Category is invalid');
        }

        return $categories;
    }

    public function getCategoryValidParent($category, $parentSlug = null)
    {
        return $category?->parent?->slug === $parentSlug;
    }


    public function findCategoryHierarchy($hierarchy, $slugs)
    {
        if (empty($slugs)) {
            return $hierarchy;
        }
        $slug = array_shift($slugs);
        foreach ($hierarchy as $category) {
            if ($category['slug'] === $slug) {
                return $this->findCategoryHierarchy($category['children'], $slugs);
            }
        }
        return null;
    }

}